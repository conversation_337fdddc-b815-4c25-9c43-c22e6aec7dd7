# 智能主检规则匹配器

这是一个基于Excel规则表的智能主检规则匹配系统，能够根据项目名和检查值自动匹配相应的规则。

## 功能特点

1. **规则匹配**: 根据项目名（XMMC字段）和检查值进行智能匹配
2. **多种比较操作**: 支持包含、不包含、大于、小于、等于等多种比较操作
3. **逻辑关系处理**: 支持AND、OR逻辑关系的复杂规则组合
4. **自动提示**: 当找不到匹配规则时，提示需要人工补充

## 文件结构

```
├── intelligent_inspection_matcher.py  # 主要的匹配器类
├── simple_test.py                     # 简单测试脚本
├── example_usage.py                   # 详细使用示例
├── data/
│   └── 智能主检规则.xlsx              # 规则数据文件
└── README_智能主检规则匹配器.md       # 本说明文件
```

## 快速开始

### 1. 基本使用

```python
from intelligent_inspection_matcher import IntelligentInspectionMatcher

# 创建匹配器实例
matcher = IntelligentInspectionMatcher()

# 进行规则匹配
result = matcher.match_rule("浅表器官彩超-甲状腺拟诊", "甲状腺光点稍增粗")

# 查看结果
print(f"匹配成功: {result['success']}")
print(f"消息: {result['message']}")
if result['name']:
    print(f"匹配的规则: {result['name']}")
```

### 2. 运行测试

```bash
# 运行简单测试
python simple_test.py

# 运行详细示例
python example_usage.py
```

## 规则表结构

Excel文件包含以下字段：

- **XH**: 序号
- **NAME**: 规则名称（匹配成功时返回的值）
- **XMMC**: 项目名称（用于匹配输入的项目名）
- **COMPARE1**: 比较操作符（包含、不包含、>=、>、<=、<、不大于、不小于、不等于）
- **VALUE1**: 比较值
- **LINK**: 逻辑关系（AND、OR、空值）

## 匹配逻辑

1. **项目匹配**: 首先根据XMMC字段匹配输入的项目名
2. **条件评估**: 根据COMPARE1和VALUE1字段评估检查值是否符合条件
3. **逻辑组合**: 根据LINK字段的值进行逻辑组合：
   - `AND`: 与关系，所有条件都必须满足
   - `OR`或空值: 或关系，满足任一条件即可
4. **结果返回**: 返回匹配成功的NAME字段值

## 支持的比较操作

- **包含**: 检查值中包含规则值
- **不包含**: 检查值中不包含规则值
- **>=**: 检查值大于等于规则值（数值比较）
- **>**: 检查值大于规则值（数值比较）
- **<=**: 检查值小于等于规则值（数值比较）
- **<**: 检查值小于规则值（数值比较）
- **不大于**: 检查值不大于规则值（等同于<=）
- **不小于**: 检查值不小于规则值（等同于>=）
- **不等于**: 检查值不等于规则值

## 返回结果格式

```python
{
    'success': bool,        # 是否匹配成功
    'message': str,         # 结果消息
    'name': str or list     # 匹配的规则名称（单个或多个）
}
```

## 使用示例

### 示例1: 甲状腺检查

```python
result = matcher.match_rule("浅表器官彩超-甲状腺拟诊", "甲状腺光点稍增粗")
# 结果: {'success': True, 'message': '匹配成功，找到4个结果', 'name': ['甲状腺光点稍增粗@', ...]}
```

### 示例2: 内分泌系统检查

```python
result = matcher.match_rule("内分泌系统", "桥本氏甲状腺炎")
# 结果: {'success': True, 'message': '匹配成功，找到11个结果', 'name': ['桥本氏甲状腺炎', ...]}
```

### 示例3: 未找到匹配项

```python
result = matcher.match_rule("不存在的项目", "测试值")
# 结果: {'success': False, 'message': '未找到项目名"不存在的项目"的匹配规则，需要人工补充', 'name': None}
```

## 注意事项

1. **数据类型**: 系统会自动处理字符串和数值类型的比较
2. **错误处理**: 当数值比较失败时，会输出错误信息但不会中断程序
3. **规则优先级**: 同一个NAME的多个规则会按照LINK字段的逻辑关系进行组合
4. **性能**: 系统会加载整个Excel文件到内存，适合中等规模的规则集

## 依赖项

- pandas: 用于Excel文件读取和数据处理
- numpy: 用于数据类型处理

## 安装依赖

```bash
pip install pandas numpy openpyxl
```

## 扩展功能

如需扩展功能，可以：

1. 添加新的比较操作符
2. 支持更复杂的逻辑关系
3. 添加规则缓存机制
4. 支持多个Excel文件
5. 添加规则验证功能

## 联系方式

如有问题或建议，请联系开发团队。
