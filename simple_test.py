#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试智能主检规则匹配器
"""

from intelligent_inspection_matcher import IntelligentInspectionMatcher


def simple_test():
    """简单测试"""
    
    # 创建匹配器实例
    matcher = IntelligentInspectionMatcher()
    
    print("智能主检规则匹配器测试")
    print("=" * 40)
    
    # 测试用例
    test_cases = [
        {
            "name": "甲状腺检查-包含匹配",
            "item_name": "浅表器官彩超-甲状腺拟诊",
            "check_value": "甲状腺光点稍增粗"
        },
        {
            "name": "内分泌系统-包含匹配",
            "item_name": "内分泌系统", 
            "check_value": "桥本氏甲状腺炎"
        },
        {
            "name": "内分泌系统-不包含匹配",
            "item_name": "内分泌系统",
            "check_value": "正常检查结果"
        },
        {
            "name": "不存在的项目",
            "item_name": "不存在的检查项目",
            "check_value": "测试值"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        print("-" * 30)
        
        result = matcher.match_rule(test_case['item_name'], test_case['check_value'])
        
        print(f"项目名: {test_case['item_name']}")
        print(f"检查值: {test_case['check_value']}")
        print(f"匹配成功: {'是' if result['success'] else '否'}")
        print(f"消息: {result['message']}")
        
        if result['name']:
            if isinstance(result['name'], list):
                print(f"匹配的规则数量: {len(result['name'])}")
                print(f"前3个匹配规则: {', '.join(result['name'][:3])}")
                if len(result['name']) > 3:
                    print(f"... 还有 {len(result['name']) - 3} 个")
            else:
                print(f"匹配的规则: {result['name']}")


def test_specific_case(item_name: str, check_value: str):
    """测试特定案例"""
    
    matcher = IntelligentInspectionMatcher()
    
    print(f"\n测试特定案例:")
    print(f"项目名: {item_name}")
    print(f"检查值: {check_value}")
    print("-" * 40)
    
    result = matcher.match_rule(item_name, check_value)
    
    print(f"匹配成功: {'是' if result['success'] else '否'}")
    print(f"消息: {result['message']}")
    
    if result['name']:
        if isinstance(result['name'], list):
            print(f"匹配的规则数量: {len(result['name'])}")
            for i, name in enumerate(result['name'][:5], 1):
                print(f"  {i}. {name}")
            if len(result['name']) > 5:
                print(f"  ... 还有 {len(result['name']) - 5} 个")
        else:
            print(f"匹配的规则: {result['name']}")
    
    return result


if __name__ == "__main__":
    # 运行简单测试
    simple_test()
    
    # 测试特定案例
    print("\n" + "=" * 50)
    test_specific_case("浅表器官彩超-甲状腺拟诊", "甲状腺光点稍增粗")
