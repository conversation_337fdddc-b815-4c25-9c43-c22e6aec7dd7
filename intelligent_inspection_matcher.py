import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple


class IntelligentInspectionMatcher:
    """智能主检规则匹配器"""
    
    def __init__(self, excel_path: str = "data/智能主检规则.xlsx"):
        """
        初始化匹配器
        
        Args:
            excel_path: Excel文件路径
        """
        self.excel_path = excel_path
        self.rules_df = None
        self.load_rules()
    
    def load_rules(self):
        """加载规则数据"""
        try:
            self.rules_df = pd.read_excel(self.excel_path)
            print(f"成功加载规则数据，共{len(self.rules_df)}条记录")
        except Exception as e:
            raise Exception(f"加载规则文件失败: {e}")
    
    def _evaluate_condition(self, compare_op: str, rule_value: str, check_value: str) -> bool:
        """
        评估单个条件是否满足
        
        Args:
            compare_op: 比较操作符
            rule_value: 规则中的值
            check_value: 待检查的值
            
        Returns:
            bool: 条件是否满足
        """
        try:
            if compare_op == "包含":
                return str(rule_value) in str(check_value)
            elif compare_op == "不包含":
                return str(rule_value) not in str(check_value)
            elif compare_op == ">=":
                return float(check_value) >= float(rule_value)
            elif compare_op == ">":
                return float(check_value) > float(rule_value)
            elif compare_op == "<=":
                return float(check_value) <= float(rule_value)
            elif compare_op == "<":
                return float(check_value) < float(rule_value)
            elif compare_op == "不大于":
                return float(check_value) <= float(rule_value)
            elif compare_op == "不小于":
                return float(check_value) >= float(rule_value)
            elif compare_op == "不等于":
                return str(check_value) != str(rule_value)
            else:
                print(f"未知的比较操作符: {compare_op}")
                return False
        except (ValueError, TypeError) as e:
            print(f"条件评估错误: {e}, 比较操作: {compare_op}, 规则值: {rule_value}, 检查值: {check_value}")
            return False
    
    def _evaluate_rule_group(self, rule_group: pd.DataFrame, item_name: str, check_value: str) -> bool:
        """
        评估一组规则（同一个NAME的所有条件）

        Args:
            rule_group: 同一个NAME的规则组
            item_name: 项目名
            check_value: 检查值

        Returns:
            bool: 规则组是否匹配成功
        """
        conditions_results = []

        for idx, rule in rule_group.iterrows():
            # 评估条件
            condition_result = self._evaluate_condition(
                rule['COMPARE1'],
                rule['VALUE1'],
                check_value
            )

            link = rule['LINK']
            conditions_results.append({
                'result': condition_result,
                'link': link,
                'rule': rule,
                'xmmc': rule['XMMC']
            })

        if not conditions_results:
            return False

        # 根据LINK字段计算最终结果
        return self._calculate_final_result(conditions_results, item_name)
    
    def _calculate_final_result(self, conditions_results: List[Dict], target_item_name: str) -> bool:
        """
        根据LINK字段计算最终结果

        Args:
            conditions_results: 条件结果列表
            target_item_name: 目标项目名

        Returns:
            bool: 最终匹配结果
        """
        if not conditions_results:
            return False

        # 找到匹配目标项目名的条件作为起始点
        target_conditions = []
        other_conditions = []

        for condition in conditions_results:
            if condition['xmmc'] == target_item_name:
                target_conditions.append(condition)
            else:
                other_conditions.append(condition)

        # 如果没有找到匹配的项目名条件，返回False
        if not target_conditions:
            return False

        # 计算目标项目名条件的结果（可能有多个，用OR连接）
        target_result = False
        for condition in target_conditions:
            target_result = target_result or condition['result']

        # 如果目标条件不满足，直接返回False
        if not target_result:
            return False

        # 处理其他条件（通常是AND关系的排除条件）
        final_result = target_result
        for condition in other_conditions:
            link = condition['link']
            current_result = condition['result']

            if link == 'AND':
                # AND关系
                final_result = final_result and current_result
            elif pd.isna(link) or link == 'OR':
                # OR关系或空值表示或关系
                final_result = final_result or current_result

        return final_result
    
    def match_rule(self, item_name: str, check_value: str) -> Dict[str, Any]:
        """
        匹配规则

        Args:
            item_name: 项目名
            check_value: 检查值

        Returns:
            Dict: 匹配结果
        """
        if self.rules_df is None:
            return {
                'success': False,
                'message': '规则数据未加载',
                'name': None
            }

        # 查找包含该项目名的所有规则
        matching_rules = self.rules_df[self.rules_df['XMMC'] == item_name]

        if matching_rules.empty:
            return {
                'success': False,
                'message': f'未找到项目名"{item_name}"的匹配规则，需要人工补充',
                'name': None
            }

        # 按NAME分组处理，每个NAME代表一个完整的规则集
        results = []
        for name, group in self.rules_df.groupby('NAME'):
            # 检查这个规则集是否包含目标项目名
            if item_name in group['XMMC'].values:
                if self._evaluate_rule_group(group, item_name, check_value):
                    results.append(name)

        if results:
            return {
                'success': True,
                'message': f'匹配成功，找到{len(results)}个结果',
                'name': results[0] if len(results) == 1 else results
            }
        else:
            return {
                'success': False,
                'message': '条件不符合，匹配失败',
                'name': None
            }


def main():
    """测试函数"""
    matcher = IntelligentInspectionMatcher()

    # 测试用例
    test_cases = [
        ("浅表器官彩超-甲状腺拟诊", "甲状腺光点稍增粗"),
        ("内分泌系统", "桥本氏甲状腺炎"),
        ("不存在的项目", "测试值")
    ]

    for item_name, check_value in test_cases:
        print(f"\n测试: 项目名='{item_name}', 检查值='{check_value}'")
        result = matcher.match_rule(item_name, check_value)
        print(f"结果: {result}")

    # 交互式测试
    print("\n" + "="*50)
    print("交互式测试 (输入 'quit' 退出)")
    print("="*50)

    while True:
        try:
            item_name = input("\n请输入项目名: ").strip()
            if item_name.lower() == 'quit':
                break

            check_value = input("请输入检查值: ").strip()
            if check_value.lower() == 'quit':
                break

            result = matcher.match_rule(item_name, check_value)

            print(f"\n匹配结果:")
            print(f"  成功: {result['success']}")
            print(f"  消息: {result['message']}")
            if result['name']:
                if isinstance(result['name'], list):
                    print(f"  匹配的规则名称: {', '.join(result['name'])}")
                else:
                    print(f"  匹配的规则名称: {result['name']}")

        except KeyboardInterrupt:
            print("\n程序已退出")
            break
        except Exception as e:
            print(f"发生错误: {e}")


if __name__ == "__main__":
    main()
