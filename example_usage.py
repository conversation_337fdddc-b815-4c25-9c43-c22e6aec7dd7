#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能主检规则匹配器使用示例

这个示例展示了如何使用IntelligentInspectionMatcher类来匹配检查规则。
"""

from intelligent_inspection_matcher import IntelligentInspectionMatcher


def example_usage():
    """使用示例"""
    
    # 创建匹配器实例
    matcher = IntelligentInspectionMatcher()
    
    print("智能主检规则匹配器使用示例")
    print("=" * 50)
    
    # 示例1: 甲状腺相关检查
    print("\n示例1: 甲状腺相关检查")
    print("-" * 30)
    
    item_name = "浅表器官彩超-甲状腺拟诊"
    check_value = "甲状腺光点稍增粗"
    
    result = matcher.match_rule(item_name, check_value)
    
    print(f"项目名: {item_name}")
    print(f"检查值: {check_value}")
    print(f"匹配结果: {result['success']}")
    print(f"消息: {result['message']}")
    if result['name']:
        if isinstance(result['name'], list):
            print(f"匹配的规则: {', '.join(result['name'][:3])}{'...' if len(result['name']) > 3 else ''}")
        else:
            print(f"匹配的规则: {result['name']}")
    
    # 示例2: 内分泌系统检查
    print("\n示例2: 内分泌系统检查")
    print("-" * 30)
    
    item_name = "内分泌系统"
    check_value = "桥本氏甲状腺炎"
    
    result = matcher.match_rule(item_name, check_value)
    
    print(f"项目名: {item_name}")
    print(f"检查值: {check_value}")
    print(f"匹配结果: {result['success']}")
    print(f"消息: {result['message']}")
    if result['name']:
        if isinstance(result['name'], list):
            print(f"匹配的规则: {', '.join(result['name'][:3])}{'...' if len(result['name']) > 3 else ''}")
        else:
            print(f"匹配的规则: {result['name']}")
    
    # 示例3: 不存在的项目
    print("\n示例3: 不存在的项目")
    print("-" * 30)
    
    item_name = "不存在的检查项目"
    check_value = "测试值"
    
    result = matcher.match_rule(item_name, check_value)
    
    print(f"项目名: {item_name}")
    print(f"检查值: {check_value}")
    print(f"匹配结果: {result['success']}")
    print(f"消息: {result['message']}")
    
    # 示例4: 数值类型的检查
    print("\n示例4: 数值类型的检查（假设）")
    print("-" * 30)
    
    # 这里需要根据实际数据中的数值类型项目来调整
    item_name = "血糖"  # 假设的项目名
    check_value = "6.5"
    
    result = matcher.match_rule(item_name, check_value)
    
    print(f"项目名: {item_name}")
    print(f"检查值: {check_value}")
    print(f"匹配结果: {result['success']}")
    print(f"消息: {result['message']}")
    if result['name']:
        if isinstance(result['name'], list):
            print(f"匹配的规则: {', '.join(result['name'][:3])}{'...' if len(result['name']) > 3 else ''}")
        else:
            print(f"匹配的规则: {result['name']}")


def interactive_demo():
    """交互式演示"""
    
    matcher = IntelligentInspectionMatcher()
    
    print("\n" + "=" * 50)
    print("交互式演示 (输入 'quit' 退出)")
    print("=" * 50)
    print("提示: 可以尝试以下项目名:")
    print("  - 浅表器官彩超-甲状腺拟诊")
    print("  - 内分泌系统")
    print("  - 浅表器官彩超-甲状腺(赠送)拟诊")
    
    while True:
        try:
            print("\n" + "-" * 30)
            item_name = input("请输入项目名: ").strip()
            if item_name.lower() == 'quit':
                break
            
            check_value = input("请输入检查值: ").strip()
            if check_value.lower() == 'quit':
                break
            
            result = matcher.match_rule(item_name, check_value)
            
            print(f"\n匹配结果:")
            print(f"  成功: {'是' if result['success'] else '否'}")
            print(f"  消息: {result['message']}")
            if result['name']:
                if isinstance(result['name'], list):
                    print(f"  匹配的规则名称:")
                    for i, name in enumerate(result['name'][:5], 1):  # 只显示前5个
                        print(f"    {i}. {name}")
                    if len(result['name']) > 5:
                        print(f"    ... 还有 {len(result['name']) - 5} 个结果")
                else:
                    print(f"  匹配的规则名称: {result['name']}")
            
        except KeyboardInterrupt:
            print("\n程序已退出")
            break
        except Exception as e:
            print(f"发生错误: {e}")


if __name__ == "__main__":
    # 运行使用示例
    example_usage()
    
    # 运行交互式演示
    interactive_demo()
