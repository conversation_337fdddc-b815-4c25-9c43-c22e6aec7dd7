#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能主检规则匹配器API接口

提供简单的函数接口，方便集成到其他系统中使用。
"""

from intelligent_inspection_matcher import IntelligentInspectionMatcher
from typing import Dict, Any, Optional


# 全局匹配器实例（单例模式）
_matcher_instance: Optional[IntelligentInspectionMatcher] = None


def get_matcher() -> IntelligentInspectionMatcher:
    """
    获取匹配器实例（单例模式）
    
    Returns:
        IntelligentInspectionMatcher: 匹配器实例
    """
    global _matcher_instance
    if _matcher_instance is None:
        _matcher_instance = IntelligentInspectionMatcher()
    return _matcher_instance


def match_inspection_rule(item_name: str, check_value: str) -> Dict[str, Any]:
    """
    匹配智能主检规则
    
    Args:
        item_name: 项目名称
        check_value: 检查值
        
    Returns:
        Dict: 匹配结果
            - success: bool, 是否匹配成功
            - message: str, 结果消息
            - name: str or list, 匹配的规则名称
    """
    matcher = get_matcher()
    return matcher.match_rule(item_name, check_value)


def is_rule_matched(item_name: str, check_value: str) -> bool:
    """
    简单判断是否匹配成功
    
    Args:
        item_name: 项目名称
        check_value: 检查值
        
    Returns:
        bool: 是否匹配成功
    """
    result = match_inspection_rule(item_name, check_value)
    return result['success']


def get_matched_rule_names(item_name: str, check_value: str) -> list:
    """
    获取匹配的规则名称列表
    
    Args:
        item_name: 项目名称
        check_value: 检查值
        
    Returns:
        list: 匹配的规则名称列表，如果没有匹配则返回空列表
    """
    result = match_inspection_rule(item_name, check_value)
    if not result['success'] or not result['name']:
        return []
    
    if isinstance(result['name'], list):
        return result['name']
    else:
        return [result['name']]


def get_first_matched_rule(item_name: str, check_value: str) -> Optional[str]:
    """
    获取第一个匹配的规则名称
    
    Args:
        item_name: 项目名称
        check_value: 检查值
        
    Returns:
        str or None: 第一个匹配的规则名称，如果没有匹配则返回None
    """
    rule_names = get_matched_rule_names(item_name, check_value)
    return rule_names[0] if rule_names else None


def batch_match_rules(items: list) -> list:
    """
    批量匹配规则
    
    Args:
        items: 项目列表，每个项目是一个包含'item_name'和'check_value'的字典
        
    Returns:
        list: 匹配结果列表
    """
    results = []
    for item in items:
        item_name = item.get('item_name', '')
        check_value = item.get('check_value', '')
        result = match_inspection_rule(item_name, check_value)
        results.append({
            'item_name': item_name,
            'check_value': check_value,
            'result': result
        })
    return results


def reload_rules(excel_path: str = None) -> bool:
    """
    重新加载规则数据
    
    Args:
        excel_path: Excel文件路径，如果为None则使用默认路径
        
    Returns:
        bool: 是否加载成功
    """
    global _matcher_instance
    try:
        if excel_path:
            _matcher_instance = IntelligentInspectionMatcher(excel_path)
        else:
            _matcher_instance = IntelligentInspectionMatcher()
        return True
    except Exception as e:
        print(f"重新加载规则失败: {e}")
        return False


# 便捷函数别名
match_rule = match_inspection_rule
is_matched = is_rule_matched
get_rule_names = get_matched_rule_names
get_first_rule = get_first_matched_rule


def demo_api():
    """API使用演示"""
    
    print("智能主检规则匹配器 API 演示")
    print("=" * 40)
    
    # 测试用例
    test_cases = [
        ("浅表器官彩超-甲状腺拟诊", "甲状腺光点稍增粗"),
        ("内分泌系统", "桥本氏甲状腺炎"),
        ("不存在的项目", "测试值")
    ]
    
    for item_name, check_value in test_cases:
        print(f"\n测试: {item_name} -> {check_value}")
        print("-" * 30)
        
        # 使用不同的API函数
        is_match = is_matched(item_name, check_value)
        rule_names = get_rule_names(item_name, check_value)
        first_rule = get_first_rule(item_name, check_value)
        full_result = match_rule(item_name, check_value)
        
        print(f"是否匹配: {is_match}")
        print(f"匹配规则数量: {len(rule_names)}")
        print(f"第一个规则: {first_rule}")
        print(f"完整结果: {full_result['message']}")
    
    # 批量匹配演示
    print(f"\n批量匹配演示:")
    print("-" * 30)
    
    batch_items = [
        {"item_name": "浅表器官彩超-甲状腺拟诊", "check_value": "甲状腺光点稍增粗"},
        {"item_name": "内分泌系统", "check_value": "桥本氏甲状腺炎"}
    ]
    
    batch_results = batch_match_rules(batch_items)
    for result in batch_results:
        print(f"{result['item_name']} -> {result['check_value']}: {result['result']['success']}")


if __name__ == "__main__":
    demo_api()
